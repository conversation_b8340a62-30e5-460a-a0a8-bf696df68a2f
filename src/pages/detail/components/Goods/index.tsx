import { Swiper, SwiperItem, View, Image } from '@tarojs/components'
import { useEffect, useState, forwardRef, useImperativeHandle } from 'react'
import zhengImg from '@/assets/images/detail/zheng.png'
import { CanvasRes, computePrice, ComputePriceRes, getCanvas, ReadyTemplateSaveRes, templateSave } from '@/api/detail'
import { useAsyncFn, useUpdateEffect } from 'react-use'
import useObjState from '@/hooks/useObjState'

export const tabs: { name: string; key: 'readyImageList' | 'printingImageList' }[] = [
  {
    name: '成衣',
    key: 'readyImageList'
  },
  {
    name: '印花',
    key: 'printingImageList'
  }
]

export interface GoodsRef {
  templateSaveFetch: () => Promise<any>
}

const Goods = forwardRef<GoodsRef, { id: string; data: ReadyTemplateSaveRes['data'] }>(({ id, data }, ref) => {
  console.log('id, data', id, data)
  const [activeGender, setActiveGender] = useState(data.gender)
  const [activeColor, setActiveColor] = useState(data.colour)
  const [activePrint, setActivePrint] = useState(data.printingProcessId)
  const styles = useObjState<CanvasRes['data']['genders']>([])
  const colors = useObjState<CanvasRes['data']['colours']>([])
  const prints = useObjState<CanvasRes['data']['printingProcessList']>([])
  const compute = useObjState<ComputePriceRes['data'] | null>(null)
  const activeTab = useObjState<'readyImageList' | 'printingImageList'>('readyImageList')
  const previewIndex = useObjState(0) // 预览图

  // const [canvasState, canvasFetch] = useAsyncFn(async () => {
  //   const res = await getCanvas(data.templateCode)
  //   console.log('response', res)
  //   styles.set(res.data.genders)
  //   colors.set(res.data.colours)
  //   prints.set(res.data.printingProcessList)
  //   return res
  // }, [])

  const [computePriceState, computePriceFetch] = useAsyncFn(async () => {
    const diyData = JSON.parse(data.diyData)
    const res = await computePrice({
      gender: activeGender,
      masterTemplateCode: data.templateCode,
      styleTemplateCode: data.styleCode,
      frontDiyData: JSON.stringify(diyData[0]) || '',
      backDiyData: JSON.stringify(diyData[1]) || '',
      frontPrintingId: activePrint,
      backPrintingId: activePrint,
      buyNum: 1,
      sizeCode: 'S'
    })
    console.log('response', res)
    compute.set(res.data)
    return res
  }, [activeGender, activePrint])

  // useEffect(() => {
  //   canvasFetch()
  // }, [])

  useUpdateEffect(() => {
    computePriceFetch()
  }, [activeGender, activePrint])

  const [templateSaveState, templateSaveFetch] = useAsyncFn(async () => {
    const currentComputeData = compute.val || data.computePriceData

    const params = {
      title: data.title,
      imageUrl: data.imageUrl,
      price: currentComputeData.totalPrice,
      diyData: data.diyData,
      templateCode: data.templateCode,
      styleCode: data.styleCode,
      colour: activeColor,
      gender: activeGender,
      tagIds: data.tagList?.map((tag) => tag.id),
      printingImage: data.printingImage,
      computePriceData: currentComputeData,
      channel: 'payment'
    }

    const res = await templateSave(params)
    return res
  }, [data, activeColor, activeGender, activePrint, compute.val])

  useImperativeHandle(
    ref,
    () => ({
      templateSaveFetch
    }),
    [templateSaveFetch]
  )

  const { imageUrl = '', printingImage = '' } = data || { imageUrl: '', printingImage: '' }
  const readyImageList = imageUrl.split(';')
  const printingImageList = printingImage.split(';')

  return (
    <>
      <div className="z-0 h-[870px] w-full bg-[#E5E7E9] box-border pb-[30px] relative flex_center">
        {/* <img className="w-[566px] h-[566px] object-contain" src={previewIndex.val} alt="" /> */}
        <Swiper
          current={previewIndex.val}
          className="w-[750px] h-[812px]"
          indicatorColor="#999"
          indicatorActiveColor="#333"
          circular
          onChange={(e) => {
            const currentIndex = e.detail.current
            previewIndex.set(currentIndex)

            // 根据当前索引自动切换tab
            if (currentIndex < readyImageList.length) {
              activeTab.set('readyImageList')
            } else {
              activeTab.set('printingImageList')
            }
          }}
        >
          {[...readyImageList, ...printingImageList].map((item, index) => (
            <SwiperItem key={index} className="h-[870px] w-[750px] flex_center">
              <Image mode="aspectFit" className="w-[566px] h-[566px] flex_center" src={item} />
            </SwiperItem>
          ))}
        </Swiper>
        <div className="absolute bottom-[48px] left-0 w-full h-[190px] z-50">
          <div className="h-[40px] flex px-[24px] box-border">
            {tabs.map((tab) => {
              return (
                <div
                  key={tab.key}
                  className={`w-[100px] h-[40px] flex_center rounded-full font-normal text-[20px] text-black leading-[20px] text-center not-italic ${activeTab.val === tab.key ? 'bg-white' : ''}`}
                  onClick={() => {
                    activeTab.set(tab.key)
                    // 切换tab时跳转到对应分类的第一张图片
                    if (tab.key === 'readyImageList') {
                      previewIndex.set(0)
                    } else if (tab.key === 'printingImageList') {
                      previewIndex.set(readyImageList.length)
                    }
                  }}
                >
                  {tab.name}
                </div>
              )
            })}
          </div>
          {activeTab.val === 'readyImageList' ? (
            <div className="mt-[20px] flex overflow-x-auto no-scrollbar px-[24px]">
              {readyImageList.map((cheng, index) => {
                const isSelected = previewIndex.val === index
                return (
                  <div
                    key={index}
                    onClick={() => previewIndex.set(index)}
                    className={`w-[130px] h-[128px] rounded-[20px] bg-[#AFBBC7] border-2 border-solid flex_center mr-[12px] ${
                      isSelected ? 'border-[#000000]' : 'border-[rgba(0,0,0,0.1)]'
                    }`}
                  >
                    <img className="w-[112px] h-[112px] object-contain" src={cheng} alt="" />
                  </div>
                )
              })}
            </div>
          ) : null}
          {activeTab.val === 'printingImageList' ? (
            <div className="mt-[20px] flex overflow-x-auto no-scrollbar px-[24px]">
              {printingImageList.map((hua, index) => {
                const currentIndex = readyImageList.length + index
                const isSelected = previewIndex.val === currentIndex
                return (
                  <div
                    key={index}
                    onClick={() => previewIndex.set(currentIndex)}
                    className={`w-[130px] h-[128px] rounded-[20px] bg-[#AFBBC7] border-2 border-solid flex_center mr-[12px] ${
                      isSelected ? 'border-[#000000]' : 'border-[rgba(0,0,0,0.1)]'
                    }`}
                  >
                    <img className="w-[112px] h-[112px] object-contain" src={hua} alt="" />
                  </div>
                )
              })}
            </div>
          ) : null}
        </div>
      </div>

      <div className="bg-white px-[18px]">
        <div className="pt-[30px]">
          {/* <div className="mb-[30px] flex items-center">
            <div className="w-[110px] h-[60px] flex_center font-normal text-[24px] text-[#202020] opacity-50 leading-[34px] tracking-[2px] text-left not-italic ml-[10px] mr-[18px]">
              T恤款式
            </div>
            <div className="flex">
              {styles.val.map((style) => (
                <div
                  key={style}
                  onClick={() => setActiveGender(style)}
                  className={`mr-[12px] w-[120px] h-[60px] rounded-[8px] border-2 border-solid flex_center font-normal text-[24px] text-black leading-[34px] text-left not-italic ${activeGender === style ? 'border-[#000000] opacity-100' : 'border-[rgba(0,0,0,0.1)] opacity-50'}`}
                >
                  {GenderMap[style]}
                </div>
              ))}
            </div>
          </div>

          <div className="mb-[30px] flex items-center">
            <div className="w-[110px] h-[60px] flex_center font-normal text-[24px] text-[#202020] opacity-50 leading-[34px] tracking-[2px] text-left not-italic ml-[10px] mr-[18px]">
              T恤颜色
            </div>
            <div className="flex">
              {colors.val.map((color) => (
                <div
                  key={color}
                  onClick={() => setActiveColor(color)}
                  className={`mr-[12px] w-[120px] h-[60px] rounded-[8px] border-2 border-solid flex_center ${activeColor === color ? 'border-[#000000]' : 'border-[rgba(0,0,0,0.1)]'}`}
                >
                  <div
                    className="color_shadow w-[24px] h-[24px] rounded-full mr-[6px]"
                    style={{ backgroundColor: ColourMap[color]?.color }}
                  ></div>
                  <div
                    className={`font-normal text-[24px] text-black leading-[34px] text-left not-italic ${activeColor === color ? 'opacity-100' : 'opacity-50'}`}
                  >
                    {ColourMap[color]?.title}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="mb-[40px] flex items-start">
            <div className="w-[110px] h-[60px] flex_center font-normal text-[24px] text-[#202020] opacity-50 leading-[34px] tracking-[2px] text-left not-italic ml-[10px] mr-[18px] flex-shrink-0">
              印花工艺
            </div>
            <div className="flex-1 w-0">
              <div className="overflow-x-auto overflow-y-hidden flex no-scrollbar">
                {prints.val.map((print) => (
                  <div
                    key={print.name}
                    onClick={() => setActivePrint(print.id)}
                    className={`mr-[12px] w-[188px] overflow-hidden h-[160px] rounded-[8px] border-2 border-solid flex_center flex-col flex-shrink-0 ${activePrint === print.id ? 'border-[#000000]' : 'border-[rgba(0,0,0,0.1)]'}`}
                  >
                    <Image mode="aspectFit" className="w-[188px] h-[106px] object-contain" src={print.imageUrl} />
                    <div
                      className={`flex-1 w-full font-normal text-[24px] text-black leading-[34px] flex_center not-italic ${activePrint === print.id ? 'opacity-100' : 'opacity-50'}`}
                    >
                      <div className="mr-[18px]">{print.name}</div>
                      <div className="text-[18px]">¥{print.price / 100}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div> */}

          {/* 价格 */}
          <div className="w-[716px] h-[306px] rounded-[16px] bg-[#F8F8F8] flex flex-col mb-[40px]">
            <div className="flex-1 h-0 px-[38px] flex flex-col justify-center">
              <div className="mb-[26px] flex items-end">
                <div className="font-semibold text-[40px] text-[#E40032] leading-none text-left not-italic">¥</div>
                <div className="font-semibold text-[60px] text-[#E40032] leading-none text-left not-italic">
                  {(compute.val?.totalPrice || data.computePriceData.totalPrice) / 100}
                </div>
                <div className="font-normal text-[24px] text-[#202020] leading-[34px] text-left not-italic line-through opacity-50 ml-[10px]">
                  优惠前¥{data.delPrice / 100}
                </div>
                <div className="flex-1 text-right font-normal text-[24px] text-[#202020] leading-[34px] not-italic opacity-50">
                  已售800+
                </div>
              </div>
              <div className="font-semibold text-[36px] text-[#202020] leading-[50px] text-left not-italic mb-[6px]">{data.title}</div>
              {/* <div className="font-normal text-[24px] text-[#202020] leading-[34px] text-left not-italic opacity-50">{data.des}</div> */}
              <div className="font-normal text-[24px] text-[#202020] leading-[44px] text-left not-italic my-[10px]">
                设计者：{data.designUser}
              </div>
            </div>
            <div className="w-full h-[76px] rounded-[0px_0px_16px_16px] bg-gradient-zheng flex items-center">
              <img className="w-[42px] h-[42px] ml-[34px] mr-[4px]" src={zhengImg} alt="" />
              <div className="font-normal text-[24px] text-[#202020] leading-[34px] text-left not-italic">
                甄选好料 · 精准还原 · 潮流设计 · 贴心售后
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
})

export default Goods
