import { useEffect, useState } from 'react'
import { Input, Swiper, SwiperItem, View, Image } from '@tarojs/components'
import Taro, { useDidShow, useRouter } from '@tarojs/taro'
import { useAsyncFn, useUpdateEffect } from 'react-use'
import { orderCreate, orderCreatInfo, OrderCreatInfoRes } from '@/api/order'
import { Expired, expiredList, ExpiredListRes } from '@/api/pay'
import addrImg from '@/assets/images/order/addr.png'
import moveImg from '@/assets/images/order/move.png'
import warnImg from '@/assets/images/order/warn.png'
import arrowImg from '@/assets/images/pay/arrow.png'
import { wxPay } from '@/utils'
import { ActionSheet, Divider, Radio } from '@taroify/core'
import useObjState from '@/hooks/useObjState'
import { computePrice, ComputePriceRes } from '@/api/detail'
import NavBarTitle from './components/NavBarTitle'
import SubmitBtn from './components/SubmitBtn'
import ConpouItem from '../coupon/components/ConpouItem'
import UseConpouItem from '../no-usecoupon/components/ConpouItem'

export const tabs: { name: string; key: 'readyImageList' | 'printingImageList' }[] = [
  {
    name: '成衣',
    key: 'readyImageList'
  },
  {
    name: '印花',
    key: 'printingImageList'
  }
]

const Pay = () => {
  const activeTab = useObjState<'readyImageList' | 'printingImageList'>('readyImageList')
  const previewIndex = useObjState(0) // 预览图
  const selectSize = useObjState('') // 选中的尺码
  const num = useObjState<number | ''>(1) // 数量，默认为1
  const compute = useObjState<ComputePriceRes['data']>({
    totalNum: 0,
    totalPrice: 0,
    frontPrintingId: 0,
    frontPrinting: '',
    frontArea: 0,
    frontPrice: 0,
    backPrintingId: 0,
    backPrinting: '',
    backArea: 0,
    backPrice: 0,
    basePrice: 0
  })
  const userAddress = useObjState<OrderCreatInfoRes['data']['userAddress'] | null>(null)
  const expired = useObjState<Expired[]>([])
  const chooseExpired = useObjState<Expired | null>(null)

  const router = useRouter()
  const [open, setOpen] = useState(false)

  const [orderCreatInfoState, orderCreatInfoFetch] = useAsyncFn(async () => {
    console.log(router.params.id)
    if (!router.params.id) return
    const res = await orderCreatInfo(router.params.id)
    console.log('response', res)
    selectSize.set(res.data.costDetail.sizeCode)
    compute.set(res.data.costDetail)
    res.data.userAddress && userAddress.set(res.data.userAddress)
    return res.data
  }, [])

  useEffect(() => {
    orderCreatInfoFetch()
  }, [])

  const [expiredListState, expiredListFetch] = useAsyncFn(async () => {
    const res = await expiredList({ ...compute.val, totalNum: num.val || compute.val.totalNum })
    console.log('expiredListState', res)
    expired.set(res.data || [])
    if (res.data[0] && res.data[0].status === 1) {
      chooseExpired.set(res.data[0] || null)
    }
    return res.data
  }, [compute.val, num.val])

  useUpdateEffect(() => {
    expiredListFetch()
  }, [compute.val, num.val])

  const [computePriceState, computePriceFetch] = useAsyncFn(async () => {
    if (!orderCreatInfoState.value || !selectSize.val) return
    const diyData = JSON.parse(orderCreatInfoState.value.diyData)
    const res = await computePrice({
      gender: orderCreatInfoState.value.gender,
      masterTemplateCode: orderCreatInfoState.value.templateCode,
      styleTemplateCode: orderCreatInfoState.value.styleCode,
      frontDiyData: JSON.stringify(diyData[0]) || '',
      backDiyData: JSON.stringify(diyData[1]) || '',
      frontPrintingId: orderCreatInfoState.value.costDetail.frontPrintingId,
      backPrintingId: orderCreatInfoState.value.costDetail.backPrintingId,
      buyNum: 1,
      sizeCode: selectSize.val
    })
    console.log('response', res)
    if (res.code !== 200) {
      Taro.showToast({
        title: res.msg,
        icon: 'none'
      })
      return null
    }
    compute.set(res.data)
    return res
  }, [orderCreatInfoState.value, selectSize.val])

  useUpdateEffect(() => {
    computePriceFetch()
  }, [orderCreatInfoState.value, selectSize.val])

  const [orderCreateState, orderCreateFetch] = useAsyncFn(async () => {
    if (!router.params.id || !orderCreatInfoState.value || !userAddress.val) return
    const res = await orderCreate({
      openId: Taro.getStorageSync('__weapp_open_id__'),
      designTemplateId: Number(router.params.id),
      size: selectSize.val,
      costDetail: {
        ...compute.val,
        couponReducePrice: chooseExpired.val?.reducePrice || 0,
        totalNum: num.val || 1
      },
      couponId: chooseExpired.val?.id || 0,
      addressId: userAddress.val.id,
      totalPrice: compute.val.totalPrice * (num.val || 1)
    })
    if (res.code !== 200) {
      Taro.showToast({
        title: res.msg || '创建订单失败',
        icon: 'none'
      })
      return 0
    }

    return res.data
  }, [orderCreatInfoState.value, selectSize.val, num.val, userAddress.val, chooseExpired.val])

  const pay = async () => {
    if (!userAddress.val) {
      Taro.showToast({
        title: '请选择收货地址',
        icon: 'none'
      })
      return
    }
    // 创建订单
    const orderId = await orderCreateFetch()
    if (!orderId) {
      return
    }
    // 获取支付参数
    const payCode = await wxPay(orderId)
    // if (payCode === 1) {
    // 无论成功与否都跳转到订单列表，不然用户会一直创建重复订单
    // 重新获取订单列表
    Taro.redirectTo({
      url: '/pages/order/index'
    })
    // }
  }

  useDidShow(async () => {
    const selectedAddress = await new Promise((resolve) => {
      Taro.getStorage({ key: 'selectedAddress' })
        .then((res) => {
          console.log(res)
          resolve(res.data)
        })
        .catch((err) => {
          console.log(err)
          resolve(null)
        })
    })

    if (selectedAddress) {
      userAddress.set(selectedAddress as OrderCreatInfoRes['data']['userAddress'])
      // 更新订单地址
      Taro.removeStorageSync('selectedAddress')
    }
  })

  const fixNum = num.val || 1

  console.log('userAddress.val', userAddress.val)

  const { readyImageList, printingImageList } = orderCreatInfoState.value || { readyImageList: [], printingImageList: [] }

  return (
    <>
      <div className="w-full h-screen overflow-hidden flex flex-col bg-[#F8F8F8]">
        <NavBarTitle />
        {orderCreatInfoState.value && (
          <div className="relative flex-1 overflow-auto bg-[#F8F8F8]">
            <div className="z-0 h-[870px] w-full bg-[#E5E7E9] box-border pb-[30px] relative flex_center">
              {/* <img className="w-[566px] h-[566px] object-contain" src={previewIndex.val} alt="" /> */}
              <Swiper
                current={previewIndex.val}
                className="w-[750px] h-[812px]"
                indicatorColor="#999"
                indicatorActiveColor="#333"
                circular
                onChange={(e) => {
                  const currentIndex = e.detail.current
                  previewIndex.set(currentIndex)

                  // 根据当前索引自动切换tab
                  if (currentIndex < readyImageList.length) {
                    activeTab.set('readyImageList')
                  } else {
                    activeTab.set('printingImageList')
                  }
                }}
              >
                {[...readyImageList, ...printingImageList].map((item, index) => {
                  // 判断是否为成衣图片，并获取对应的工艺名称
                  const isReadyImage = index < readyImageList.length
                  let craftName = ''
                  if (isReadyImage) {
                    craftName = index === 0 ? compute.val.frontPrinting : index === 1 ? compute.val.backPrinting : ''
                  }

                  return (
                    <SwiperItem key={index} className="h-[870px] w-[750px] flex_center relative">
                      {/* 工艺标签 - 只在成衣图片上显示 */}
                      {isReadyImage && craftName && (
                        <div className="absolute top-[80px] right-[60px] z-10">
                          <div className="relative bg-[#666666] rounded-[20px] px-[20px] py-[12px] flex items-center shadow-lg">
                            {/* 白色圆点 */}
                            <div className="w-[12px] h-[12px] bg-white rounded-full mr-[8px]"></div>
                            {/* 标签文字 */}
                            <span className="text-white text-[20px] font-medium leading-[28px] whitespace-nowrap">{craftName}</span>
                            {/* 左侧三角形箭头 */}
                            <div className="absolute left-[-8px] top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-[8px] border-b-[8px] border-r-[8px] border-t-transparent border-b-transparent border-r-[#666666]"></div>
                          </div>
                        </div>
                      )}
                      <Image mode="aspectFit" className="w-[566px] h-[566px] flex_center" src={item} />
                    </SwiperItem>
                  )
                })}
              </Swiper>
              <div className="absolute bottom-[48px] left-0 w-full h-[190px] z-50">
                <div className="h-[40px] flex px-[24px] box-border">
                  {tabs.map((tab) => {
                    return (
                      <div
                        key={tab.key}
                        className={`w-[100px] h-[40px] flex_center rounded-full font-normal text-[20px] text-black leading-[20px] text-center not-italic ${activeTab.val === tab.key ? 'bg-white' : ''}`}
                        onClick={() => {
                          activeTab.set(tab.key)
                          // 切换tab时跳转到对应分类的第一张图片
                          if (tab.key === 'readyImageList') {
                            previewIndex.set(0)
                          } else if (tab.key === 'printingImageList') {
                            previewIndex.set(readyImageList.length)
                          }
                        }}
                      >
                        {tab.name}
                      </div>
                    )
                  })}
                </div>
                {activeTab.val === 'readyImageList' ? (
                  <div className="mt-[20px] flex overflow-x-auto no-scrollbar px-[24px]">
                    {readyImageList.map((cheng: string, index: number) => {
                      const isSelected = previewIndex.val === index
                      return (
                        <div
                          key={index}
                          onClick={() => previewIndex.set(index)}
                          className={`w-[130px] h-[128px] rounded-[20px] bg-[#AFBBC7] border-2 border-solid flex_center mr-[12px] ${
                            isSelected ? 'border-[#000000]' : 'border-[rgba(0,0,0,0.1)]'
                          }`}
                        >
                          <img className="w-[112px] h-[112px] object-contain" src={cheng} alt="" />
                        </div>
                      )
                    })}
                  </div>
                ) : null}
                {activeTab.val === 'printingImageList' ? (
                  <div className="mt-[20px] flex overflow-x-auto no-scrollbar px-[24px]">
                    {printingImageList.map((hua: string, index: number) => {
                      const currentIndex = readyImageList.length + index
                      const isSelected = previewIndex.val === currentIndex
                      return (
                        <div
                          key={index}
                          onClick={() => previewIndex.set(currentIndex)}
                          className={`w-[130px] h-[128px] rounded-[20px] bg-[#AFBBC7] border-2 border-solid flex_center mr-[12px] ${
                            isSelected ? 'border-[#000000]' : 'border-[rgba(0,0,0,0.1)]'
                          }`}
                        >
                          <img className="w-[112px] h-[112px] object-contain" src={hua} alt="" />
                        </div>
                      )
                    })}
                  </div>
                ) : null}
              </div>
            </div>
            <div className="z-10 relative mt-[-30px] bg-white w-full shadow-[0px_4_10px_0px_rgba(0,0,0,0.06)] rounded-[24px_24px_0_0] px-[25px] box-border pt-[28px] pb-[18px]">
              <div
                onClick={() => {
                  Taro.navigateTo({
                    url: '/pages/address/index?isCheck=true'
                  })
                }}
                className="h-[80px] flex_center mb-[8px]"
              >
                <img className="w-[32px] h-[32px] self-start mt-[3px]" src={addrImg} alt="" />
                {userAddress.val ? (
                  <div className="h-full ml-[10px] mr-[20px] flex-1 w-0 font-medium text-[28px] text-black leading-[40px] text-left not-italic line-clamp-2">
                    {userAddress.val.contacts} {userAddress.val.phone} {userAddress.val.province}
                    {userAddress.val.city}
                    {userAddress.val.area}
                    {userAddress.val.street}
                    {userAddress.val.detail}
                  </div>
                ) : (
                  <div className="h-full ml-[10px] mr-[20px] flex-1 w-0 font-medium text-[28px] text-black leading-[40px] text-left not-italic line-clamp-2">
                    请选择收货地址
                  </div>
                )}
                <img className="w-[32px] h-[32px]" src={moveImg} alt="" />
              </div>
              <div className="font-normal text-[24px] text-black opacity-60 leading-[34px] text-left not-italic mb-[34px] box-border pl-[42px]">
                {orderCreatInfoState.value.expressTips}
              </div>
              <div className="w-[680px] h-[2px] opacity-10 bg-black"></div>

              <div className="flex items-start mt-[44px] mb-[10px]">
                <div className="w-[84px] h-[60px] font-medium text-[28px] text-black leading-[60px] text-left not-italic mr-[30px]">
                  尺码：
                </div>
                <div className="flex flex-wrap flex-1 w-0">
                  {orderCreatInfoState.value.patternSize.map((size) => (
                    <div
                      key={size.id}
                      onClick={() => selectSize.set(size.size)}
                      className={`mr-[20px] mb-[30px] w-[120px] h-[60px] rounded-[8px] bg-[#F8F8F8] border-2 border-solid flex_center font-normal text-[24px] text-black leading-[34px] text-left not-italic ${selectSize.val === size.size ? 'border-[#000000]' : 'border-[rgba(0,0,0,0.1)]'}`}
                    >
                      {size.size}
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex items-center mb-[38px]">
                <div className="w-[84px] h-[60px] font-medium text-[28px] text-black leading-[60px] text-left not-italic mr-[30px]">
                  数量：
                </div>
                <div className="flex flex-1 w-0">
                  <div className="w-[226px] h-[60px] rounded-[8px] bg-[#F8F8F8] border-2 border-solid border-[rgba(0,0,0,0.1)] flex_center">
                    <div onClick={() => num.set(fixNum - 1 < 1 ? 1 : fixNum - 1)} className="w-[50px] flex_center">
                      -
                    </div>
                    <div className="w-[2px] h-[34px] opacity-20 bg-black"></div>
                    <Input
                      onInput={(e) =>
                        num.set(Number(e.detail.value) < 1 ? '' : Number(e.detail.value) > 9999 ? 9999 : Number(e.detail.value))
                      }
                      onBlur={() => num.set(fixNum < 1 ? 1 : fixNum)}
                      value={`${num.val}`}
                      type="number"
                      maxlength={4}
                      className="flex-1 w-0 flex_center font-normal text-[28px] text-black leading-[20px] text-center not-italic"
                    />
                    <div className="w-[2px] h-[34px] opacity-20 bg-black"></div>
                    <div onClick={() => num.set(fixNum + 1 > 9999 ? 9999 : fixNum + 1)} className="w-[50px] flex_center">
                      +
                    </div>
                  </div>
                </div>
              </div>

              <div className="w-[680px] h-[2px] opacity-10 bg-black mb-[44px]"></div>

              <div className="flex justify-between mb-[24px]">
                <div className="font-normal text-[24px] text-black leading-[34px] text-left not-italic">T恤： 件数 x{num.val}</div>
                <div className="font-medium text-[28px] text-black leading-[40px] text-right not-italic">
                  ¥{(compute.val.basePrice * fixNum) / 100}
                </div>
              </div>
              <div className="flex justify-between mb-[24px]">
                <div className="font-normal text-[24px] text-black leading-[34px] text-left not-italic">
                  正面：{compute.val.frontPrinting}
                  <span className="text-[#9f9f9f]">{compute.val.frontArea}cm</span>
                </div>
                <div className="font-medium text-[28px] text-black leading-[40px] text-right not-italic">
                  ¥{(compute.val.frontPrice * fixNum) / 100}
                </div>
              </div>
              <div className="flex justify-between mb-[24px]">
                <div className="font-normal text-[24px] text-black leading-[34px] text-left not-italic">
                  背面：{compute.val.backPrinting}
                  <span className="text-[#9f9f9f]">{compute.val.backArea}cm</span>
                </div>
                <div className="font-medium text-[28px] text-black leading-[40px] text-right not-italic">
                  ¥{(compute.val.backPrice * fixNum) / 100}
                </div>
              </div>

              <div className="flex justify-between mb-[24px]">
                <div className="font-normal text-[24px] text-black leading-[34px] text-left not-italic">优惠:</div>
              </div>

              {chooseExpired.val?.id ? (
                <div onClick={() => setOpen(true)} className="mx-[-20px]">
                  <ConpouItem
                    data={chooseExpired.val}
                    showCount={false}
                    content={<div className="w-full h-full flex items-center justify-end">已选中</div>}
                  ></ConpouItem>
                </div>
              ) : (
                <div
                  className="flex h-[80px] px-[20px] rounded-[8px] bg-[#f1f1f1] justify-between mb-[24px] items-center"
                  onClick={() => {
                    Taro.showToast({
                      title: '暂无可用优惠券',
                      icon: 'none'
                    })
                  }}
                >
                  <div className="font-normal text-[24px] text-[#777777] leading-[34px] text-left not-italic flex items-center">
                    暂无可用优惠券
                  </div>
                  <div className="font-medium text-[28px] text-black leading-[40px] text-right not-italic flex items-center">
                    <img className="w-[30px] h-[30px]" src={arrowImg} alt="" />
                  </div>
                </div>
              )}

              <div className="flex justify-between mb-[50px]">
                <div className="font-normal text-[24px] text-[#E40633] leading-[34px] text-left not-italic">总计：</div>
                <div className="font-medium text-[36px] text-[#E40633] leading-[40px] text-right not-italic">
                  ¥{(compute.val.totalPrice * fixNum - (chooseExpired.val?.reducePrice || 0)) / 100}
                </div>
              </div>

              <div className="flex items-center">
                <img src={warnImg} className="w-[24px] h-[24px] mr-[12px]" alt="" />
                <div className="font-normal text-[24px] text-[#E40633] leading-[34px] text-left not-italic">
                  {orderCreatInfoState.value.waringTips}
                </div>
              </div>
            </div>
          </div>
        )}
        <SubmitBtn pay={pay} totalPrice={(compute.val.totalPrice * fixNum - (chooseExpired.val?.reducePrice || 0)) / 100} />
      </div>

      <ActionSheet open={open} onSelect={() => setOpen(false)} onClose={setOpen}>
        {expired.val.map((item, index) => {
          if (item.status === 1) {
            return (
              <div
                key={index}
                onClick={() => {
                  chooseExpired.set(item)
                  setOpen(false)
                }}
              >
                <ConpouItem
                  key={item.id}
                  data={item}
                  content={
                    <div className="w-full h-full flex items-center justify-end">
                      <Radio.Group value={chooseExpired.val?.id === item.id ? '1' : ''} className="coupon-check">
                        <Radio name="1"></Radio>
                      </Radio.Group>
                    </div>
                  }
                ></ConpouItem>
              </div>
            )
          } else {
            return <UseConpouItem key={item.id} data={item} state="不可用"></UseConpouItem>
          }
        })}
        <ActionSheet.Button onClick={() => setOpen(false)}>取消</ActionSheet.Button>
      </ActionSheet>
    </>
  )
}

export default Pay
